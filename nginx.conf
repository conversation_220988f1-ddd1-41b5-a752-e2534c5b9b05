user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 20m;
    gzip on;
    gzip_disable "msie6";
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 美诗词网站配置
    server {
        listen 80;
        server_name mokk.cn www.mokk.cn;

        # 重定向到HTTPS
        location / {
            return 301 https://$host$request_uri;
        }
    }

    server {
        listen 443 ssl http2;
        server_name mokk.cn www.mokk.cn;

        # SSL证书配置 (需要替换为实际的证书路径)
        ssl_certificate /etc/nginx/ssl/mokk.cn.pem;
        ssl_certificate_key /etc/nginx/ssl/mokk.cn.key;

        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:50m;
        ssl_session_tickets off;

        # 现代TLS配置
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
        ssl_prefer_server_ciphers on;

        # HSTS设置
        add_header Strict-Transport-Security "max-age=63072000" always;

        # 静态文件缓存设置
        location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg|woff|woff2|ttf|otf)$ {
            root /root/meisici/shici001/dist/client;
            try_files $uri $uri/ @proxy;
            expires 7d;
            add_header Cache-Control "public, max-age=604800";
        }

        # 处理 _astro 静态资源
        location /_astro/ {
            root /root/meisici/shici001/dist/client;
            try_files $uri $uri/ @proxy;
            expires 7d;
            add_header Cache-Control "public, max-age=604800";
        }

        # 处理字体文件
        location /fonts/ {
            root /root/meisici/shici001/dist/client;
            try_files $uri $uri/ @proxy;
            expires 30d;
            add_header Cache-Control "public, max-age=2592000";
        }

        # 反向代理到Node.js应用
        location @proxy {
            proxy_pass http://localhost:8080;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # 默认反向代理
        location / {
            try_files $uri $uri/ @proxy;
        }
    }
}