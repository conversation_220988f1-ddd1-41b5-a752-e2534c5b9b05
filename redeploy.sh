#!/bin/bash

# 美诗词网站完整重新部署脚本 - 修复生产环境问题

echo "=== 开始完整重新部署美诗词网站 ==="

# 设置错误时退出
set -e

# 获取当前目录
CURRENT_DIR=$(pwd)
echo "当前目录: $CURRENT_DIR"

# 1. 停止现有服务
echo "=== 停止现有服务 ==="
pm2 stop meishici 2>/dev/null || echo "PM2应用未运行"
pm2 delete meishici 2>/dev/null || echo "PM2应用不存在"

# 2. 清理旧的构建文件
echo "=== 清理旧构建文件 ==="
rm -rf dist/
rm -rf node_modules/.vite/
rm -rf .astro/

# 3. 设置环境变量
echo "=== 设置环境变量 ==="
export NODE_ENV=production
export NODE_OPTIONS="--max-old-space-size=8192"

# 4. 安装依赖
echo "=== 安装依赖 ==="
yarn install --frozen-lockfile

# 5. 重新构建SQLite依赖
echo "=== 重新构建SQLite依赖 ==="
yarn rebuild better-sqlite3 --update-binary

# 6. 构建项目
echo "=== 构建项目 ==="
yarn build

# 7. 检查构建结果
if [ ! -f "dist/server/entry.mjs" ]; then
  echo "❌ 构建失败，服务器入口文件不存在"
  exit 1
fi

if [ ! -d "dist/client" ]; then
  echo "❌ 构建失败，客户端文件不存在"
  exit 1
fi

echo "✅ 构建成功"

# 8. 检查静态文件
echo "=== 检查静态文件 ==="
if [ -f "public/fonts/汇文明朝体.otf" ]; then
  echo "✅ 字体文件存在"
else
  echo "⚠️  字体文件不存在，但继续部署"
fi

# 9. 更新Nginx配置
echo "=== 更新Nginx配置 ==="
if [ -f "nginx.conf" ]; then
  sudo cp nginx.conf /etc/nginx/nginx.conf
  sudo nginx -t
  if [ $? -eq 0 ]; then
    echo "✅ Nginx配置测试通过"
    sudo systemctl reload nginx
    echo "✅ Nginx已重新加载"
  else
    echo "❌ Nginx配置测试失败"
    exit 1
  fi
else
  echo "⚠️  nginx.conf文件不存在，跳过Nginx配置更新"
fi

# 10. 启动PM2应用
echo "=== 启动PM2应用 ==="
pm2 start ecosystem.config.js --env production

# 11. 保存PM2配置
pm2 save

# 12. 检查应用状态
echo "=== 检查应用状态 ==="
sleep 5
pm2 status

# 13. 测试应用响应
echo "=== 测试应用响应 ==="
if curl -f http://localhost:8080 > /dev/null 2>&1; then
  echo "✅ 应用响应正常"
else
  echo "❌ 应用无响应，检查日志："
  pm2 logs meishici --lines 20
  exit 1
fi

echo "=== 部署完成 ==="
echo "✅ 网站已成功部署到生产环境"
echo "🌐 访问地址: https://mokk.cn"
echo "📊 监控命令: pm2 monit"
echo "📝 查看日志: pm2 logs meishici"
echo ""
echo "如果仍有问题，请检查："
echo "1. pm2 logs meishici"
echo "2. sudo nginx -t"
echo "3. sudo systemctl status nginx"
