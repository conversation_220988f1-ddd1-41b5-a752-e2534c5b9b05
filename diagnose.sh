#!/bin/bash

# 美诗词网站部署问题诊断脚本

echo "=== 美诗词网站部署诊断 ==="
echo "时间: $(date)"
echo ""

# 1. 检查基本环境
echo "1. 基本环境检查"
echo "=================="
echo "Node.js版本: $(node --version)"
echo "Yarn版本: $(yarn --version)"
echo "PM2版本: $(pm2 --version 2>/dev/null || echo '未安装')"
echo "Nginx版本: $(nginx -v 2>&1 | head -1)"
echo "当前目录: $(pwd)"
echo "当前用户: $(whoami)"
echo ""

# 2. 检查环境变量
echo "2. 环境变量检查"
echo "==============="
echo "NODE_ENV: ${NODE_ENV:-未设置}"
echo "PORT: ${PORT:-未设置}"
echo ""

# 3. 检查项目文件
echo "3. 项目文件检查"
echo "==============="
if [ -f "package.json" ]; then
  echo "✅ package.json 存在"
else
  echo "❌ package.json 不存在"
fi

if [ -f "astro.config.mjs" ]; then
  echo "✅ astro.config.mjs 存在"
else
  echo "❌ astro.config.mjs 不存在"
fi

if [ -f "ecosystem.config.js" ]; then
  echo "✅ ecosystem.config.js 存在"
else
  echo "❌ ecosystem.config.js 不存在"
fi

if [ -d "dist" ]; then
  echo "✅ dist 目录存在"
  if [ -f "dist/server/entry.mjs" ]; then
    echo "✅ 服务器入口文件存在"
  else
    echo "❌ 服务器入口文件不存在"
  fi
  
  if [ -d "dist/client" ]; then
    echo "✅ 客户端文件目录存在"
    echo "   客户端文件数量: $(find dist/client -type f | wc -l)"
  else
    echo "❌ 客户端文件目录不存在"
  fi
else
  echo "❌ dist 目录不存在"
fi
echo ""

# 4. 检查PM2状态
echo "4. PM2状态检查"
echo "=============="
if command -v pm2 >/dev/null 2>&1; then
  pm2 status
  echo ""
  echo "PM2进程详情:"
  pm2 describe meishici 2>/dev/null || echo "meishici应用不存在"
else
  echo "❌ PM2未安装"
fi
echo ""

# 5. 检查端口占用
echo "5. 端口占用检查"
echo "==============="
echo "端口8080占用情况:"
netstat -tlnp | grep :8080 || echo "端口8080未被占用"
echo ""

# 6. 检查Nginx状态
echo "6. Nginx状态检查"
echo "================"
if command -v nginx >/dev/null 2>&1; then
  echo "Nginx状态: $(systemctl is-active nginx 2>/dev/null || echo '未知')"
  echo "Nginx配置测试:"
  nginx -t 2>&1
else
  echo "❌ Nginx未安装"
fi
echo ""

# 7. 检查应用响应
echo "7. 应用响应检查"
echo "==============="
echo "本地8080端口响应:"
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080 2>/dev/null; then
  echo "✅ 应用响应正常"
else
  echo "❌ 应用无响应"
fi

echo ""
echo "外部域名响应:"
if curl -s -o /dev/null -w "%{http_code}" https://mokk.cn 2>/dev/null; then
  echo "✅ 外部访问正常"
else
  echo "❌ 外部访问失败"
fi
echo ""

# 8. 检查日志
echo "8. 最近日志检查"
echo "==============="
if command -v pm2 >/dev/null 2>&1; then
  echo "PM2最近日志:"
  pm2 logs meishici --lines 10 --nostream 2>/dev/null || echo "无PM2日志"
else
  echo "PM2未安装，无法查看日志"
fi
echo ""

# 9. 检查磁盘空间
echo "9. 系统资源检查"
echo "==============="
echo "磁盘使用情况:"
df -h . | head -2
echo ""
echo "内存使用情况:"
free -h
echo ""

# 10. 建议
echo "10. 诊断建议"
echo "============"
if [ ! -d "dist" ]; then
  echo "🔧 需要运行构建: yarn build"
fi

if ! pm2 describe meishici >/dev/null 2>&1; then
  echo "🔧 需要启动PM2应用: pm2 start ecosystem.config.js"
fi

if ! curl -s http://localhost:8080 >/dev/null 2>&1; then
  echo "🔧 应用未响应，检查PM2日志: pm2 logs meishici"
fi

echo ""
echo "=== 诊断完成 ==="
