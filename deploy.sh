#!/bin/bash

# 美诗词网站腾讯云轻量应用服务器部署脚本

echo "=== 开始部署美诗词网站 ==="

# 创建必要的目录
mkdir -p /root/logs

# 安装依赖
echo "=== 安装依赖 ==="
yarn install

# 设置生产环境变量
echo "=== 设置环境变量 ==="
export NODE_ENV=production

# 构建项目
echo "=== 构建项目 ==="
yarn build

# 检查构建结果
if [ ! -d "dist" ]; then
  echo "构建失败，dist目录不存在"
  exit 1
fi

# 设置Nginx配置
echo "=== 配置Nginx ==="
if [ ! -d "/etc/nginx/ssl" ]; then
  mkdir -p /etc/nginx/ssl
  echo "创建SSL证书目录"
fi

# 如果ssl证书不存在，提醒用户
if [ ! -f "/etc/nginx/ssl/mokk.cn.pem" ] || [ ! -f "/etc/nginx/ssl/mokk.cn.key" ]; then
  echo "警告: SSL证书文件不存在，请将证书文件放置在/etc/nginx/ssl/目录下"
  echo "证书文件应命名为: mokk.cn.pem 和 mokk.cn.key"
fi

# 复制Nginx配置
cp nginx.conf /etc/nginx/nginx.conf
echo "已更新Nginx配置"

# 重启Nginx
echo "=== 重启Nginx ==="
nginx -t && systemctl restart nginx

# 使用PM2部署应用
echo "=== 使用PM2部署Node应用 ==="
# 检查PM2是否安装
if ! command -v pm2 &> /dev/null; then
  echo "PM2未安装，正在安装..."
  npm install -g pm2
fi

# 停止现有应用（如果存在）
pm2 stop meishici 2>/dev/null
pm2 delete meishici 2>/dev/null

# 启动应用
pm2 start ecosystem.config.js --env production
pm2 save

# 设置开机自启
echo "=== 配置PM2开机自启 ==="
pm2 startup
systemctl enable pm2-root

echo "=== 部署完成 ==="
echo "网站已部署在 https://mokk.cn"
echo "可以通过 pm2 logs meishici 查看应用日志"
echo "可以通过 pm2 monit 监控应用状态"